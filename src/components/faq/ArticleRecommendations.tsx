import React from "react";
import Link from "next/link";
import { FaqMeta } from "@/types/faq";

interface ArticleRecommendationsProps {
  series: FaqMeta[];
  continueReading: FaqMeta[];
  className?: string;
  variant?: "sidebar" | "bottom";
}

const ArticleRecommendations: React.FC<ArticleRecommendationsProps> = ({
  series,
  continueReading,
  className = "",
  variant = "sidebar",
}) => {
  // 侧边栏样式 - 推荐系列文章（只显示标题）
  if (variant === "sidebar") {
    return (
      <div className={`space-y-6 ${className}`}>
        {series.length > 0 && (
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
              <span className="w-1 h-5 bg-[#FFA015] rounded-full mr-3"></span>
              推荐系列文章
            </h3>
            <div className="space-y-3">
              {series.slice(0, 5).map((article, index) => (
                <Link
                  key={article.id}
                  href={`/faq/${article.slug}`}
                  className="block group"
                >
                  <div className="flex items-start gap-3 p-3 rounded-md hover:bg-gray-50 transition-colors duration-200">
                    <span className="flex-shrink-0 w-6 h-6 bg-[#FFF8E7] text-[#FFA015] rounded-full flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </span>
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 group-hover:text-[#FFA015] transition-colors duration-200 leading-tight">
                        <span className="line-clamp-2">{article.question}</span>
                      </h4>
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  // 底部样式 - 继续阅读（包括标题和简介）
  return (
    <div className={`space-y-6 ${className}`}>
      {continueReading.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center">
            <span className="w-1 h-5 bg-[#FFA015] rounded-full mr-3"></span>
            继续阅读
          </h3>
          <div className="grid gap-6 md:grid-cols-1 lg:grid-cols-3">
            {continueReading.slice(0, 3).map((article) => (
              <Link
                key={article.id}
                href={`/faq/${article.slug}`}
                className="block group"
              >
                <div className="p-4 rounded-lg border border-gray-200 hover:border-[#FFA015] hover:shadow-md transition-all duration-200">
                  <h4 className="text-base font-medium text-gray-900 group-hover:text-[#FFA015] transition-colors duration-200 mb-3 leading-tight">
                    <span className="line-clamp-2">{article.question}</span>
                  </h4>
                  {article.description && (
                    <p className="text-sm text-gray-600 leading-relaxed">
                      <span className="line-clamp-3">
                        {article.description
                          .replace(/[#*]/g, "")
                          .substring(0, 120)}
                        {article.description.length > 120 ? "..." : ""}
                      </span>
                    </p>
                  )}
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ArticleRecommendations;
