"use client";

import React from "react";
import Link from "next/link";
import { FaqMeta } from "@/types/faq";

interface ArticleRecommendationsProps {
  series: FaqMeta[];
  continueReading: FaqMeta[];
  className?: string;
}

const ArticleRecommendations: React.FC<ArticleRecommendationsProps> = ({
  series,
  continueReading,
  className = "",
}) => {
  return (
    <div className={`space-y-6 ${className}`}>
      {/* 推荐系列文章 */}
      {series.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span className="w-1 h-5 bg-[#FFA015] rounded-full mr-3"></span>
            推荐系列文章
          </h3>
          <div className="space-y-3">
            {series.map((article, index) => (
              <Link
                key={article.id}
                href={`/faq/${article.slug}`}
                className="block group"
              >
                <div className="flex items-start gap-3 p-3 rounded-md hover:bg-gray-50 transition-colors duration-200">
                  <span className="flex-shrink-0 w-6 h-6 bg-[#FFF8E7] text-[#FFA015] rounded-full flex items-center justify-center text-sm font-medium mt-0.5">
                    {index + 1}
                  </span>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 group-hover:text-[#FFA015] transition-colors duration-200 leading-tight">
                      <span className="line-clamp-2">{article.question}</span>
                    </h4>
                    {article.description && (
                      <p className="text-xs text-gray-500 mt-1 leading-tight">
                        <span className="line-clamp-2">
                          {article.description
                            .replace(/[#*]/g, "")
                            .substring(0, 80)}
                          ...
                        </span>
                      </p>
                    )}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 继续阅读 */}
      {continueReading.length > 0 && (
        <div className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <span className="w-1 h-5 bg-[#FFA015] rounded-full mr-3"></span>
            继续阅读
          </h3>
          <div className="space-y-3">
            {continueReading.map((article) => (
              <Link
                key={article.id}
                href={`/faq/${article.slug}`}
                className="block group"
              >
                <div className="flex items-start gap-3 p-3 rounded-md hover:bg-gray-50 transition-colors duration-200">
                  <div className="flex-shrink-0 w-2 h-2 bg-[#FFA015] rounded-full mt-2"></div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-sm font-medium text-gray-900 group-hover:text-[#FFA015] transition-colors duration-200 leading-tight">
                      <span className="line-clamp-2">{article.question}</span>
                    </h4>
                    {article.description && (
                      <p className="text-xs text-gray-500 mt-1 leading-tight">
                        <span className="line-clamp-2">
                          {article.description
                            .replace(/[#*]/g, "")
                            .substring(0, 80)}
                          ...
                        </span>
                      </p>
                    )}
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 空状态 */}
      {series.length === 0 && continueReading.length === 0 && (
        <div className="p-6 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <svg
              className="w-8 h-8 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
              />
            </svg>
          </div>
          <h3 className="text-sm font-medium text-gray-900 mb-1">
            暂无推荐文章
          </h3>
          <p className="text-xs text-gray-500">推荐内容正在准备中</p>
        </div>
      )}
    </div>
  );
};

export default ArticleRecommendations;
